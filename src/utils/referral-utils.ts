/**
 * Utility functions for handling referral links and storing referral data
 */

const REFERRAL_ID_KEY = "marketplace_referral_id";

/**
 * Extract referral ID from URL parameters
 */
export function extractReferralIdFromUrl(): string | null {
  if (typeof window === "undefined") {
    return null;
  }

  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get("referral_id");
}

/**
 * Store referral ID in localStorage
 */
export function storeReferralId(referralId: string): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    localStorage.setItem(REFERRAL_ID_KEY, referralId);
    console.log("Referral ID stored:", referralId);
  } catch (error) {
    console.error("Failed to store referral ID:", error);
  }
}

/**
 * Get stored referral ID from localStorage
 */
export function getStoredReferralId(): string | null {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    return localStorage.getItem(REFERRAL_ID_KEY);
  } catch (error) {
    console.error("Failed to get stored referral ID:", error);
    return null;
  }
}

/**
 * Clear stored referral ID from localStorage
 */
export function clearStoredReferralId(): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    localStorage.removeItem(REFERRAL_ID_KEY);
    console.log("Referral ID cleared");
  } catch (error) {
    console.error("Failed to clear referral ID:", error);
  }
}

/**
 * Check if there's a referral ID in the URL and store it
 * This should be called when the app loads
 */
export function handleReferralFromUrl(): string | null {
  const referralId = extractReferralIdFromUrl();
  
  if (referralId) {
    // Only store if we don't already have one stored
    const existingReferralId = getStoredReferralId();
    if (!existingReferralId) {
      storeReferralId(referralId);
      console.log("New referral ID detected and stored:", referralId);
    } else {
      console.log("Referral ID already exists, not overwriting:", existingReferralId);
    }
    return referralId;
  }

  return null;
}

/**
 * Get the referral ID to use when updating user profile
 * This returns the stored referral ID and clears it after retrieval
 */
export function consumeReferralId(): string | null {
  const referralId = getStoredReferralId();
  if (referralId) {
    clearStoredReferralId();
    console.log("Referral ID consumed:", referralId);
  }
  return referralId;
}
